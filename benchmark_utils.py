"""
简化的基准测试工具函数
"""
import json
import os
from typing import Any, Dict, List


def write_to_json(data: Dict[str, Any], filename: str) -> None:
    """
    将数据写入 JSON 文件
    """
    # 确保目录存在
    os.makedirs(os.path.dirname(filename) if os.path.dirname(filename) else ".", exist_ok=True)
    
    with open(filename, 'w', encoding='utf-8') as f:
        json.dump(data, f, indent=2, ensure_ascii=False)


def convert_to_pytorch_benchmark_format(data: Dict[str, Any]) -> Dict[str, Any]:
    """
    转换为 PyTorch 基准测试格式（简化版本）
    """
    return data


def calculate_metrics(outputs: List[Dict[str, Any]]) -> Dict[str, float]:
    """
    计算基准测试指标
    """
    if not outputs:
        return {}
    
    successful_requests = [o for o in outputs if o.get('success', False)]
    total_requests = len(outputs)
    successful_count = len(successful_requests)
    
    if successful_count == 0:
        return {
            "success_rate": 0.0,
            "total_requests": total_requests,
            "successful_requests": 0,
            "failed_requests": total_requests,
            "avg_latency": 0.0,
            "p50_latency": 0.0,
            "p95_latency": 0.0,
            "p99_latency": 0.0,
        }
    
    latencies = [o['latency'] for o in successful_requests]
    latencies.sort()
    
    def percentile(data, p):
        if not data:
            return 0.0
        k = (len(data) - 1) * p / 100
        f = int(k)
        c = k - f
        if f + 1 < len(data):
            return data[f] * (1 - c) + data[f + 1] * c
        else:
            return data[f]
    
    return {
        "success_rate": successful_count / total_requests,
        "total_requests": total_requests,
        "successful_requests": successful_count,
        "failed_requests": total_requests - successful_count,
        "avg_latency": sum(latencies) / len(latencies),
        "p50_latency": percentile(latencies, 50),
        "p95_latency": percentile(latencies, 95),
        "p99_latency": percentile(latencies, 99),
        "min_latency": min(latencies),
        "max_latency": max(latencies),
    }
