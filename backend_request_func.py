"""
简化的后端请求函数，专门用于 embedding 基准测试
"""
import asyncio
import json
import time
from dataclasses import dataclass
from typing import Any, Dict, Optional

import aiohttp


@dataclass
class RequestFuncInput:
    """请求函数的输入参数"""
    api_url: str
    prompt: str
    api_key: Optional[str] = None
    model: Optional[str] = None


@dataclass 
class RequestFuncOutput:
    """请求函数的输出结果"""
    generated_text: str = ""
    success: bool = True
    latency: float = 0.0
    ttft: float = 0.0  # Time to first token
    prompt_len: int = 0
    error: str = ""


async def async_request_openai_embeddings(
    request_func_input: RequestFuncInput,
) -> RequestFuncOutput:
    """
    异步发送 OpenAI 兼容的 embedding 请求
    """
    api_url = request_func_input.api_url
    assert api_url.endswith("/v1/embeddings")

    headers = {"Content-Type": "application/json"}
    if request_func_input.api_key:
        headers["Authorization"] = f"Bearer {request_func_input.api_key}"

    payload = {
        "input": request_func_input.prompt,
        "model": request_func_input.model or "text-embedding-ada-002",
    }

    start_time = time.perf_counter()
    
    try:
        async with aiohttp.ClientSession() as session:
            async with session.post(api_url, headers=headers, json=payload) as response:
                if response.status == 200:
                    result = await response.json()
                    end_time = time.perf_counter()
                    
                    return RequestFuncOutput(
                        generated_text="embedding_generated",  # embedding 不返回文本
                        success=True,
                        latency=end_time - start_time,
                        ttft=end_time - start_time,  # embedding 请求没有流式输出
                        prompt_len=len(request_func_input.prompt.split()),
                        error=""
                    )
                else:
                    error_text = await response.text()
                    end_time = time.perf_counter()
                    
                    return RequestFuncOutput(
                        generated_text="",
                        success=False,
                        latency=end_time - start_time,
                        ttft=0.0,
                        prompt_len=len(request_func_input.prompt.split()),
                        error=f"HTTP {response.status}: {error_text}"
                    )
                    
    except Exception as e:
        end_time = time.perf_counter()
        return RequestFuncOutput(
            generated_text="",
            success=False,
            latency=end_time - start_time,
            ttft=0.0,
            prompt_len=len(request_func_input.prompt.split()),
            error=str(e)
        )


# 请求函数映射
ASYNC_REQUEST_FUNCS = {
    "vllm-embedding": async_request_openai_embeddings,
    "openai-embedding": async_request_openai_embeddings,
}


def get_tokenizer(tokenizer_id: str, tokenizer_mode: str = "auto", trust_remote_code: bool = False):
    """
    获取 tokenizer，简化版本
    """
    try:
        from transformers import AutoTokenizer
        return AutoTokenizer.from_pretrained(
            tokenizer_id, 
            trust_remote_code=trust_remote_code
        )
    except Exception:
        # 如果无法加载 tokenizer，返回一个简单的 mock
        class MockTokenizer:
            def encode(self, text):
                return text.split()
            
            def decode(self, tokens):
                return " ".join(tokens) if isinstance(tokens, list) else str(tokens)
                
        return MockTokenizer()
