#!/usr/bin/env python3
"""
简单测试脚本，验证 benchmark_serving_embedding.py 是否可以正常导入和运行
"""

import sys
import os

def test_import():
    """测试是否可以正常导入"""
    try:
        import benchmark_serving_embedding
        print("✓ 成功导入 benchmark_serving_embedding 模块")
        return True
    except ImportError as e:
        print(f"✗ 导入失败: {e}")
        return False

def test_argument_parser():
    """测试参数解析器是否正常工作"""
    try:
        # 由于参数解析器在 __main__ 块中，我们直接测试导入是否成功
        import benchmark_serving_embedding

        # 检查一些关键的类和函数是否存在
        assert hasattr(benchmark_serving_embedding, 'main')
        assert hasattr(benchmark_serving_embedding, 'RandomDataset')
        assert hasattr(benchmark_serving_embedding, 'OPENAI_COMPATIBLE_BACKENDS')

        print("✓ 参数解析器相关功能正常")
        print("  - main 函数存在")
        print("  - RandomDataset 类存在")
        print("  - OPENAI_COMPATIBLE_BACKENDS 常量存在")
        return True
    except Exception as e:
        print(f"✗ 参数解析器测试失败: {e}")
        return False

def test_random_dataset():
    """测试 RandomDataset 类是否正常工作"""
    try:
        import benchmark_serving_embedding
        
        # 创建一个简单的 tokenizer mock
        class MockTokenizer:
            def encode(self, text):
                return text.split()
        
        # 测试 RandomDataset
        dataset = benchmark_serving_embedding.RandomDataset()
        requests = dataset.sample(
            tokenizer=MockTokenizer(),
            num_requests=2,
            input_len=10,
            output_len=1
        )
        
        print("✓ RandomDataset 工作正常")
        print(f"  - 生成了 {len(requests)} 个请求")
        if requests:
            print(f"  - 第一个请求: {requests[0].prompt[:50]}...")
        return True
    except Exception as e:
        print(f"✗ RandomDataset 测试失败: {e}")
        return False

def main():
    """运行所有测试"""
    print("开始测试 benchmark_serving_embedding.py...")
    print("=" * 50)
    
    tests = [
        ("导入测试", test_import),
        ("参数解析器测试", test_argument_parser), 
        ("RandomDataset 测试", test_random_dataset),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n运行 {test_name}:")
        if test_func():
            passed += 1
        
    print("\n" + "=" * 50)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("✓ 所有测试通过！可以尝试运行 embedding benchmark")
        print("\n建议的运行命令:")
        print("python benchmark_serving_embedding.py \\")
        print("    --host 0.0.0.0 \\")
        print("    --backend vllm-embedding \\")
        print("    --model \"bge-large-zh-v1.5\" \\")
        print("    --random-input-len 128 \\")
        print("    --num-prompts 1 \\")
        print("    --port 8000 \\")
        print("    --save-result \\")
        print("    --result-filename \"./results/test_embedding.json\" \\")
        print("    --trust-remote-code")
    else:
        print("✗ 部分测试失败，需要进一步修复")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
