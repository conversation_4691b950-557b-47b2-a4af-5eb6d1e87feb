python benchmark_serving_embedding.py \
    --host 0.0.0.0 \
    --backend vllm-embedding \
    --model "/home/<USER>/bge-large-zh-v1.5" \ 
    --random-input-len 128 \
    --num-prompts 1 \
    --port 8000 \
    --save-result \
    --result-filename "./results/test_embedding.json" \
    --trust-remote-code
INFO 07-31 19:46:50 [__init__.py:240] Automatically detected platform rocm.
usage: benchmark_serving_embedding.py [-h]
                                      [--backend {tgi,vllm,lmdeploy,deepspeed-mii,openai,openai-chat,tensorrt-llm,scalellm,sglang,vllm-embedding,openai-embedding}]
                                      [--base-url BASE_URL] [--host HOST] [--port PORT] [--endpoint ENDPOINT]
                                      [--dataset-name {random}] [--dataset-path DATASET_PATH]
                                      [--max-concurrency MAX_CONCURRENCY] --model MODEL [--tokenizer TOKENIZER]
                                      [--use-beam-search] [--num-prompts NUM_PROMPTS] [--logprobs LOGPROBS]
                                      [--request-rate REQUEST_RATE] [--burstiness BURSTINESS] [--seed SEED]
                                      [--trust-remote-code] [--disable-tqdm] [--profile] [--save-result]
                                      [--save-detailed] [--append-result] [--metadata [KEY=VALUE ...]]
                                      [--result-dir RESULT_DIR] [--result-filename RESULT_FILENAME] [--ignore-eos]
                                      [--percentile-metrics PERCENTILE_METRICS]
                                      [--metric-percentiles METRIC_PERCENTILES] [--goodput GOODPUT [GOODPUT ...]]
                                      [--random-input-len RANDOM_INPUT_LEN] [--random-output-len RANDOM_OUTPUT_LEN]
                                      [--random-range-ratio RANDOM_RANGE_RATIO]
                                      [--random-prefix-len RANDOM_PREFIX_LEN] [--top-p TOP_P] [--top-k TOP_K]
                                      [--min-p MIN_P] [--temperature TEMPERATURE]
                                      [--tokenizer-mode {auto,slow,mistral,custom}]
                                      [--served-model-name SERVED_MODEL_NAME]
                                      [--lora-modules LORA_MODULES [LORA_MODULES ...]]
benchmark_serving_embedding.py: error: unrecognized arguments:  
bash: --random-input-len: command not found