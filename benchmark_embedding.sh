#!/bin/bash

# 使用专用 embedding 测试脚本
num_prompts_list=(8 16 16 48 64 128 256 384 512 512 1024)
input_lengths=(1 2 4 8 16 32 64 128 256 512 1024)

model_dir="bge-large-zh-v1.5"
log_dir="benchmark_logs/${model_dir}"
results_dir="results"
mkdir -p "$log_dir"
mkdir -p "$results_dir"

for input_len in "${input_lengths[@]}"; do
    for num_prompts in "${num_prompts_list[@]}"; do
        log_file="${log_dir}/embedding_il${input_len}_np${num_prompts}.log"

        echo "---------------------------------------------"
        echo "Running embedding benchmark with:"
        echo "Input Length: ${input_len} | Total Prompts: ${num_prompts}"

        # 使用专用的 embedding 测试脚本
        python benchmark_serving_embedding.py \
            --host 0.0.0.0 \
            --backend vllm-embedding \
            --model bge-large-zh-v1.5 \
            --random-input-len "$input_len" \
            --num-prompts "$num_prompts" \
            --port 8000 \
            --endpoint /v1/embeddings \
            --save-result \
            --result-filename "./results/embedding_${input_len}_${num_prompts}.json" \
            --trust-remote-code 2>&1 | tee "$log_file"

        sleep 10
    done
done

echo "All embedding benchmark combinations completed!"
